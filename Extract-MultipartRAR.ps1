#Requires -Version 7.0

<#
.SYNOPSIS
    Automatically extracts and cleans up multi-part RAR archives.

.DESCRIPTION
    Scans the current directory for RAR files, identifies multi-part archives,
    extracts only those with all parts present, and deletes successfully extracted RAR files.
    Preserves incomplete multi-part sets and single RAR files.

.PARAMETER Path
    The directory to scan for RAR files. Defaults to current directory.

.PARAMETER WhatIf
    Shows what would be done without actually performing the operations.

.EXAMPLE
    .\Extract-MultipartRAR.ps1
    Extracts complete multi-part RAR archives in the current directory.

.EXAMPLE
    .\Extract-MultipartRAR.ps1 -Path "C:\Downloads" -WhatIf
    Shows what would be extracted in the specified directory without doing it.
#>

[CmdletBinding(SupportsShouldProcess)]
param(
    [Parameter()]
    [string]$Path = (Get-Location).Path,
    
    [Parameter()]
    [switch]$WhatIf
)

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to find extraction tool
function Get-ExtractionTool {
    $winrarPath = Get-Command "winrar.exe" -ErrorAction SilentlyContinue
    $sevenZipPath = Get-Command "7z.exe" -ErrorAction SilentlyContinue
    
    if ($winrarPath) {
        return @{ Tool = "WinRAR"; Path = $winrarPath.Source }
    }
    elseif ($sevenZipPath) {
        return @{ Tool = "7-Zip"; Path = $sevenZipPath.Source }
    }
    else {
        throw "Neither WinRAR nor 7-Zip found in PATH. Please install one of these tools."
    }
}

# Function to identify multi-part RAR naming patterns
function Get-MultipartGroups {
    param([System.IO.FileInfo[]]$RarFiles)
    
    $groups = @{}
    $singleFiles = @()
    
    foreach ($file in $RarFiles) {
        $fileName = $file.BaseName
        $extension = $file.Extension
        
        # Pattern 1: file.part01.rar, file.part02.rar, etc.
        if ($fileName -match '^(.+)\.part(\d+)$') {
            $baseName = $matches[1]
            $partNumber = [int]$matches[2]
            
            if (-not $groups.ContainsKey($baseName)) {
                $groups[$baseName] = @{ Files = @(); Pattern = "part"; Numbers = @() }
            }
            $groups[$baseName].Files += $file
            $groups[$baseName].Numbers += $partNumber
        }
        # Pattern 2: file.r00, file.r01, file.r02, etc. (with file.rar as first part)
        elseif ($fileName -match '^(.+)\.r(\d+)$') {
            $baseName = $matches[1]
            $partNumber = [int]$matches[2] + 1  # r00 = part 1, r01 = part 2, etc.
            
            if (-not $groups.ContainsKey($baseName)) {
                $groups[$baseName] = @{ Files = @(); Pattern = "r"; Numbers = @() }
            }
            $groups[$baseName].Files += $file
            $groups[$baseName].Numbers += $partNumber
        }
        # Check if this is the main .rar file for .r## pattern
        elseif ($extension -eq ".rar") {
            $possibleRFiles = $RarFiles | Where-Object { $_.BaseName -match "^$([regex]::Escape($fileName))\.r\d+$" }
            if ($possibleRFiles.Count -gt 0) {
                # This is part of a multi-part .r## series
                if (-not $groups.ContainsKey($fileName)) {
                    $groups[$fileName] = @{ Files = @(); Pattern = "r"; Numbers = @() }
                }
                $groups[$fileName].Files += $file
                $groups[$fileName].Numbers += 1  # .rar file is part 1
            }
            else {
                # This is a single RAR file
                $singleFiles += $file
            }
        }
        else {
            # Single RAR file or unknown pattern
            $singleFiles += $file
        }
    }
    
    return @{ Groups = $groups; Singles = $singleFiles }
}

# Function to check if all parts are present
function Test-CompleteMultipart {
    param($Group)
    
    $numbers = $Group.Numbers | Sort-Object
    $expectedCount = $numbers[-1]  # Highest number should equal total count
    
    # Check for consecutive numbering starting from 1
    for ($i = 1; $i -le $expectedCount; $i++) {
        if ($i -notin $numbers) {
            return $false
        }
    }
    
    return $true
}

# Function to extract archive
function Invoke-Extraction {
    param(
        [System.IO.FileInfo[]]$Files,
        [hashtable]$Tool,
        [string]$DestinationPath
    )
    
    # Use the first file for extraction (WinRAR and 7-Zip will automatically handle multi-part)
    $firstFile = ($Files | Sort-Object Name)[0]
    
    try {
        if ($Tool.Tool -eq "WinRAR") {
            $arguments = @("x", "-y", "`"$($firstFile.FullName)`"", "`"$DestinationPath`"")
            $process = Start-Process -FilePath $Tool.Path -ArgumentList $arguments -Wait -PassThru -NoNewWindow
        }
        else {
            # 7-Zip
            $arguments = @("x", "`"$($firstFile.FullName)`"", "-o`"$DestinationPath`"", "-y")
            $process = Start-Process -FilePath $Tool.Path -ArgumentList $arguments -Wait -PassThru -NoNewWindow
        }
        
        return $process.ExitCode -eq 0
    }
    catch {
        Write-ColorOutput "Error during extraction: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Main script execution
try {
    Write-ColorOutput "=== Multi-part RAR Archive Extractor ===" "Cyan"
    Write-ColorOutput "Scanning directory: $Path" "Yellow"
    
    # Verify path exists
    if (-not (Test-Path $Path)) {
        throw "Path does not exist: $Path"
    }
    
    # Find extraction tool
    $extractionTool = Get-ExtractionTool
    Write-ColorOutput "Using extraction tool: $($extractionTool.Tool)" "Green"
    
    # Get all RAR files
    $rarFiles = Get-ChildItem -Path $Path -Filter "*.rar" -File
    $rarFiles += Get-ChildItem -Path $Path -Filter "*.r[0-9][0-9]" -File
    
    if ($rarFiles.Count -eq 0) {
        Write-ColorOutput "No RAR files found in the specified directory." "Yellow"
        return
    }
    
    Write-ColorOutput "Found $($rarFiles.Count) RAR-related files" "White"
    
    # Group files by multi-part patterns
    $groupResult = Get-MultipartGroups -RarFiles $rarFiles
    $multipartGroups = $groupResult.Groups
    $singleFiles = $groupResult.Singles
    
    Write-ColorOutput "`nAnalysis Results:" "Cyan"
    Write-ColorOutput "- Multi-part groups found: $($multipartGroups.Count)" "White"
    Write-ColorOutput "- Single RAR files found: $($singleFiles.Count)" "White"
    
    $extractedGroups = @()
    $skippedGroups = @()
    
    # Process multi-part groups
    foreach ($groupName in $multipartGroups.Keys) {
        $group = $multipartGroups[$groupName]
        $isComplete = Test-CompleteMultipart -Group $group
        
        Write-ColorOutput "`nProcessing group: $groupName" "Yellow"
        Write-ColorOutput "  Parts found: $($group.Files.Count) ($(($group.Numbers | Sort-Object) -join ', '))" "White"
        
        if ($isComplete) {
            Write-ColorOutput "  Status: COMPLETE - Ready for extraction" "Green"
            
            if ($WhatIf) {
                Write-ColorOutput "  [WHAT-IF] Would extract and delete $($group.Files.Count) files" "Magenta"
            }
            else {
                # Extract the archive
                $extractSuccess = Invoke-Extraction -Files $group.Files -Tool $extractionTool -DestinationPath $Path
                
                if ($extractSuccess) {
                    Write-ColorOutput "  Extraction: SUCCESS" "Green"
                    $extractedGroups += $group
                    
                    # Delete the RAR files
                    foreach ($file in $group.Files) {
                        Remove-Item -Path $file.FullName -Force
                        Write-ColorOutput "  Deleted: $($file.Name)" "Gray"
                    }
                }
                else {
                    Write-ColorOutput "  Extraction: FAILED - Files preserved" "Red"
                    $skippedGroups += $group
                }
            }
        }
        else {
            Write-ColorOutput "  Status: INCOMPLETE - Skipping (missing parts)" "Red"
            $skippedGroups += $group
        }
    }
    
    # Report single files (preserved)
    if ($singleFiles.Count -gt 0) {
        Write-ColorOutput "`nSingle RAR files (preserved):" "Cyan"
        foreach ($file in $singleFiles) {
            Write-ColorOutput "  $($file.Name)" "White"
        }
    }
    
    # Summary
    Write-ColorOutput "`n=== SUMMARY ===" "Cyan"
    Write-ColorOutput "Extracted groups: $($extractedGroups.Count)" "Green"
    Write-ColorOutput "Skipped groups: $($skippedGroups.Count)" "Yellow"
    Write-ColorOutput "Preserved single files: $($singleFiles.Count)" "White"
    
    if ($WhatIf) {
        Write-ColorOutput "`n[WHAT-IF MODE] No actual changes were made." "Magenta"
    }
}
catch {
    Write-ColorOutput "Error: $($_.Exception.Message)" "Red"
    exit 1
}
